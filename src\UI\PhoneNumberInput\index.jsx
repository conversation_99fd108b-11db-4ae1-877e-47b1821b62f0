import React, { forwardRef } from "react";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import styled from "styled-components/macro";
import theme from "styled-theming";
import { colors, dark, light, textSizes, breakpoints } from "@styles/vars";
import PropTypes from "prop-types";

// Styled wrapper for the phone input
const StyledPhoneInputWrapper = styled.div`
  position: relative;
  width: 100%;

  .PhoneInput {
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px;
    border-radius: 8px;
    border: 1px solid transparent;
    background-color: ${theme("theme", {
      light: light.highlight,
      dark: dark.highlight,
    })};
    transition: border-color var(--transition), box-shadow var(--transition);
    overflow: hidden;

    &:hover {
      box-shadow: ${theme("theme", {
        light: `0 0 0 2px ${colors.primary}`,
        dark: `0 0 0 2px ${colors.primary}`,
      })};
    }

    &:focus-within {
      box-shadow: 0 0 0 2px ${colors.primary};
    }

    &.error {
      border-color: ${colors.error};
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
      box-shadow: none;
      background-color: ${theme("theme", {
        light: light.highlight,
        dark: dark.highlight,
      })};
    }
  }

  .PhoneInputCountry {
    display: flex;
    align-items: center;
    padding: 0 8px 0 12px;
    border-right: 1px solid ${theme("theme", {
      light: colors.light_gray,
      dark: colors.dark,
    })};
    cursor: pointer;
    min-width: 60px;
    height: 100%;

    &:hover {
      background-color: ${theme("theme", {
        light: "rgba(16, 145, 147, 0.05)",
        dark: "rgba(16, 145, 147, 0.1)",
      })};
    }

    ${breakpoints.mobileS} {
      min-width: 50px;
      padding: 0 6px 0 8px;
    }

    ${breakpoints.tablet} {
      min-width: 60px;
      padding: 0 8px 0 12px;
    }
  }

  .PhoneInputCountryIcon {
    width: 20px;
    height: 15px;
    margin-right: 6px;
    border-radius: 2px;
    object-fit: cover;

    ${breakpoints.mobileS} {
      width: 18px;
      height: 13px;
      margin-right: 4px;
    }

    ${breakpoints.tablet} {
      width: 20px;
      height: 15px;
      margin-right: 6px;
    }
  }

  .PhoneInputCountrySelect {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 1;
    border: none;
    opacity: 0;
    cursor: pointer;
  }

  .PhoneInputCountrySelectArrow {
    display: block;
    width: 0;
    height: 0;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 4px solid ${theme("theme", {
      light: colors.gray,
      dark: colors.gray,
    })};
    margin-left: 4px;
    transition: transform 0.2s ease;

    ${breakpoints.mobileS} {
      border-left-width: 2px;
      border-right-width: 2px;
      border-top-width: 3px;
      margin-left: 2px;
    }

    ${breakpoints.tablet} {
      border-left-width: 3px;
      border-right-width: 3px;
      border-top-width: 4px;
      margin-left: 4px;
    }
  }

  .PhoneInputInput {
    flex: 1;
    height: 100%;
    padding: 10px 16px;
    border: none;
    background: transparent;
    font-size: ${textSizes["14"]};
    color: ${theme("theme", {
      light: light.text,
      dark: dark.text,
    })};
    outline: none;

    &::placeholder {
      color: ${colors.gray};
    }

    &:disabled {
      cursor: not-allowed;
    }

    ${breakpoints.mobileS} {
      padding: 8px 12px;
      font-size: ${textSizes["13"]};
    }

    ${breakpoints.tablet} {
      padding: 10px 16px;
      font-size: ${textSizes["14"]};
    }
  }
`;

const PhoneNumberInput = forwardRef(({
  value,
  onChange,
  placeholder = "Enter phone number",
  disabled = false,
  error = false,
  id,
  className,
  defaultCountry = "NG",
  international = true,
  withCountryCallingCode = true,
  ...props
}, ref) => {
  return (
    <StyledPhoneInputWrapper className={className}>
      <PhoneInput
        ref={ref}
        id={id}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        defaultCountry={defaultCountry}
        international={international}
        withCountryCallingCode={withCountryCallingCode}
        className={`${error ? 'error' : ''} ${disabled ? 'disabled' : ''}`}
        {...props}
      />
    </StyledPhoneInputWrapper>
  );
});

PhoneNumberInput.displayName = "PhoneNumberInput";

PhoneNumberInput.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  error: PropTypes.bool,
  id: PropTypes.string,
  className: PropTypes.string,
  defaultCountry: PropTypes.string,
  international: PropTypes.bool,
  withCountryCallingCode: PropTypes.bool,
};

export default PhoneNumberInput;
