import React, { useState } from "react";
import {
  Drawer,
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Skeleton
} from "@mui/material";
import { Close } from "@mui/icons-material";
import styled from "styled-components";
import theme from "styled-theming";
import { colors, dark, light } from "@styles/vars";
import { formatFirebaseTimestamp } from "@utils/dates";

const SidebarContainer = styled(Box)`
  width: 400px;
  height: 100%;
  padding: 24px;
  background-color: ${theme("theme", {
    light: light.bodyBg,
    dark: dark.bodyBg,
  })};
  
  @media (max-width: 768px) {
    width: 100vw;
    padding: 16px;
  }
`;

const SidebarHeader = styled(Box)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid ${theme("theme", {
    light: "#e5e7eb",
    dark: "#2d313a",
  })};
`;

const SectionContainer = styled(Box)`
  margin-bottom: 24px;
`;

const SectionTitle = styled(Typography)`
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
  color: ${theme("theme", {
    light: "#1f2937",
    dark: "#f9fafb",
  })};
`;

const InfoCard = styled(Card)`
  margin-bottom: 16px;
  background-color: ${theme("theme", {
    light: light.widgetBg,
    dark: dark.widgetBg,
  })};
  border: 1px solid ${theme("theme", {
    light: "#e5e7eb",
    dark: "#2d313a",
  })};
  box-shadow: none;
`;

const MedicationChip = styled(Chip)`
  margin: 4px 4px 4px 0;
  background-color: ${colors.blue};
  color: white;
  font-size: 12px;
`;

const PatientImageContainer = styled(Box)`
  position: relative;
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }
`;

const PatientImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
`;



const PatientImageWithLoading = ({ image, index }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const handleImageClick = () => {
    if (!hasError) {
      window.open(image.url, '_blank');
    }
  };

  if (hasError) {
    return null; // Don't render anything if image failed to load
  }

  return (
    <PatientImageContainer onClick={handleImageClick}>
      {isLoading && (
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          sx={{ position: 'absolute', top: 0, left: 0 }}
        />
      )}
      <PatientImage
        src={image.url}
        alt={`Patient image ${index + 1}`}
        onLoad={handleImageLoad}
        onError={handleImageError}
        style={{ display: isLoading ? 'none' : 'block' }}
      />
    </PatientImageContainer>
  );
};

const VisitNotesSidebar = ({ open, onClose, visitNotes }) => {
  if (!visitNotes) return null;

  const {
    mood,
    appetite,
    mobility,
    medications = [],
    patientImages = [],
    completedServices = []
  } = visitNotes;

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          zIndex: 1300,
        }
      }}
    >
      <SidebarContainer>
        <SidebarHeader>
          <Typography variant="h6" fontWeight={600}>
            Visit Notes
          </Typography>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </SidebarHeader>

        {/* Completed Services Section - First */}
        {completedServices.length > 0 && (
          <SectionContainer>
            <SectionTitle>Completed Services</SectionTitle>
            {completedServices.map((service, index) => (
              <InfoCard key={index}>
                <CardContent sx={{ padding: "12px !important" }}>
                  <Typography variant="body1" fontWeight={500} gutterBottom>
                    {service.name}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Completed: {formatFirebaseTimestamp(service.completedAt)}
                  </Typography>
                </CardContent>
              </InfoCard>
            ))}
          </SectionContainer>
        )}

        {/* Patient Status Section - Equal width layout */}
        {(mood || appetite || mobility) && (
          <SectionContainer>
            <SectionTitle>Patient Status</SectionTitle>
            <Grid container spacing={2}>
              {mood && (
                <Grid item xs={12} sm={4}>
                  <InfoCard>
                    <CardContent sx={{ padding: "12px !important" }}>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        Mood
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {mood}
                      </Typography>
                    </CardContent>
                  </InfoCard>
                </Grid>
              )}

              {mobility && (
                <Grid item xs={12} sm={4}>
                  <InfoCard>
                    <CardContent sx={{ padding: "12px !important" }}>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        Mobility
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {mobility}
                      </Typography>
                    </CardContent>
                  </InfoCard>
                </Grid>
              )}

              {appetite && (
                <Grid item xs={12} sm={4}>
                  <InfoCard>
                    <CardContent sx={{ padding: "12px !important" }}>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        Appetite
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {appetite}
                      </Typography>
                    </CardContent>
                  </InfoCard>
                </Grid>
              )}
            </Grid>
          </SectionContainer>
        )}

        {/* Medications Section */}
        {medications.length > 0 && (
          <SectionContainer>
            <SectionTitle>Medications</SectionTitle>
            <Box>
              {medications.map((medication, index) => (
                <MedicationChip
                  key={index}
                  label={medication}
                  size="small"
                />
              ))}
            </Box>
          </SectionContainer>
        )}

        {/* Patient Images Section - Last */}
        {patientImages.length > 0 && (
          <SectionContainer>
            <SectionTitle>Patient Images</SectionTitle>
            <Grid container spacing={2}>
              {patientImages.map((image, index) => (
                <Grid item xs={6} key={index}>
                  <PatientImageWithLoading image={image} index={index} />
                </Grid>
              ))}
            </Grid>
          </SectionContainer>
        )}

        {/* Empty State */}
        {!mood && !appetite && !mobility && medications.length === 0 && 
         patientImages.length === 0 && completedServices.length === 0 && (
          <Box textAlign="center" py={4}>
            <Typography color="textSecondary">
              No visit notes available
            </Typography>
          </Box>
        )}
      </SidebarContainer>
    </Drawer>
  );
};

export default VisitNotesSidebar;
