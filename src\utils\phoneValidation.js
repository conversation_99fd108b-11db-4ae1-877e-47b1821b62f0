import { isValidPhoneNumber, parsePhoneNumber } from "react-phone-number-input";
import { z } from "zod";

/**
 * Validates if a phone number is valid using react-phone-number-input
 * @param {string} phoneNumber - The phone number to validate
 * @param {string} country - Optional country code for validation context
 * @returns {boolean} - True if valid, false otherwise
 */
export const isValidPhone = (phoneNumber, country) => {
  if (!phoneNumber) return false;
  
  try {
    return isValidPhoneNumber(phoneNumber, country);
  } catch (error) {
    return false;
  }
};

/**
 * Parses a phone number and returns detailed information
 * @param {string} phoneNumber - The phone number to parse
 * @returns {object|null} - Parsed phone number object or null if invalid
 */
export const parsePhone = (phoneNumber) => {
  if (!phoneNumber) return null;
  
  try {
    return parsePhoneNumber(phoneNumber);
  } catch (error) {
    return null;
  }
};

/**
 * Formats a phone number for display
 * @param {string} phoneNumber - The phone number to format
 * @param {string} format - Format type ('national', 'international', 'e164', 'rfc3966')
 * @returns {string} - Formatted phone number or original if invalid
 */
export const formatPhone = (phoneNumber, format = "international") => {
  const parsed = parsePhone(phoneNumber);
  if (!parsed) return phoneNumber;
  
  try {
    switch (format) {
      case "national":
        return parsed.formatNational();
      case "international":
        return parsed.formatInternational();
      case "e164":
        return parsed.format("E.164");
      case "rfc3966":
        return parsed.format("RFC3966");
      default:
        return parsed.formatInternational();
    }
  } catch (error) {
    return phoneNumber;
  }
};

/**
 * Zod schema for phone number validation
 * @param {object} options - Validation options
 * @param {boolean} options.required - Whether the field is required
 * @param {string} options.country - Country code for validation context
 * @param {string} options.requiredMessage - Custom required message
 * @param {string} options.invalidMessage - Custom invalid message
 * @returns {z.ZodSchema} - Zod schema for phone validation
 */
export const createPhoneSchema = ({
  required = true,
  country,
  requiredMessage = "Phone number is required",
  invalidMessage = "Please enter a valid phone number"
} = {}) => {
  let schema = z.string();
  
  if (required) {
    schema = schema.nonempty(requiredMessage);
  } else {
    schema = schema.optional();
  }
  
  return schema.refine(
    (value) => {
      if (!required && (!value || value.trim() === "")) {
        return true; // Allow empty for optional fields
      }
      return isValidPhone(value, country);
    },
    {
      message: invalidMessage,
    }
  );
};

/**
 * Default phone schema for required fields
 */
export const phoneSchema = createPhoneSchema();

/**
 * Optional phone schema
 */
export const optionalPhoneSchema = createPhoneSchema({ required: false });

/**
 * Emergency contact phone schema with custom message
 */
export const emergencyPhoneSchema = createPhoneSchema({
  requiredMessage: "Emergency contact phone is required",
  invalidMessage: "Please enter a valid emergency contact phone number"
});
